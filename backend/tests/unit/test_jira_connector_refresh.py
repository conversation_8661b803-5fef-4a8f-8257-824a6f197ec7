"""Test to verify that <PERSON>ra connector refresh frequency issue is fixed."""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, MagicMock

from onyx.background.celery.tasks.indexing.utils import _should_index
from onyx.configs.app_configs import DEFAULT_CONNECTOR_REFRESH_FREQ
from onyx.configs.constants import DocumentSource
from onyx.db.enums import ConnectorCredentialPairStatus, IndexingStatus, IndexModelStatus
from onyx.db.models import Connector, ConnectorCredentialPair, IndexAttempt, SearchSettings


def test_should_index_with_null_refresh_freq():
    """Test that connectors with null refresh_freq still get scheduled for indexing."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # This is the key issue - null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # Create a mock last index attempt that happened more than the default refresh frequency ago
    last_index = Mock(spec=IndexAttempt)
    last_index.status = IndexingStatus.SUCCESS
    last_index.time_updated = datetime.now(timezone.utc) - timedelta(seconds=DEFAULT_CONNECTOR_REFRESH_FREQ + 60)
    
    # Mock the database session
    db_session = Mock()
    
    # Mock get_db_current_time to return current time
    from onyx.background.celery.tasks.indexing import utils
    original_get_db_current_time = utils.get_db_current_time
    utils.get_db_current_time = Mock(return_value=datetime.now(timezone.utc))
    
    try:
        # Test that indexing should be triggered despite null refresh_freq
        result = _should_index(
            cc_pair=cc_pair,
            last_index=last_index,
            search_settings_instance=search_settings,
            search_settings_primary=True,
            secondary_index_building=False,
            db_session=db_session
        )
        
        assert result is True, "Connector with null refresh_freq should still be scheduled for indexing"
        
    finally:
        # Restore original function
        utils.get_db_current_time = original_get_db_current_time


def test_should_index_with_null_refresh_freq_too_recent():
    """Test that connectors with null refresh_freq don't get scheduled if last index was too recent."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # This is the key issue - null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # Create a mock last index attempt that happened recently (within default refresh frequency)
    last_index = Mock(spec=IndexAttempt)
    last_index.status = IndexingStatus.SUCCESS
    last_index.time_updated = datetime.now(timezone.utc) - timedelta(seconds=DEFAULT_CONNECTOR_REFRESH_FREQ - 60)
    
    # Mock the database session
    db_session = Mock()
    
    # Mock get_db_current_time to return current time
    from onyx.background.celery.tasks.indexing import utils
    original_get_db_current_time = utils.get_db_current_time
    utils.get_db_current_time = Mock(return_value=datetime.now(timezone.utc))
    
    try:
        # Test that indexing should NOT be triggered if last index was too recent
        result = _should_index(
            cc_pair=cc_pair,
            last_index=last_index,
            search_settings_instance=search_settings,
            search_settings_primary=True,
            secondary_index_building=False,
            db_session=db_session
        )
        
        assert result is False, "Connector should not be scheduled if last index was too recent"
        
    finally:
        # Restore original function
        utils.get_db_current_time = original_get_db_current_time


def test_should_index_no_previous_attempt():
    """Test that connectors with no previous attempts always get scheduled."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # No previous index attempt
    last_index = None
    
    # Mock the database session
    db_session = Mock()
    
    # Test that indexing should be triggered for first-time indexing
    result = _should_index(
        cc_pair=cc_pair,
        last_index=last_index,
        search_settings_instance=search_settings,
        search_settings_primary=True,
        secondary_index_building=False,
        db_session=db_session
    )
    
    assert result is True, "Connector with no previous attempts should always be scheduled"
