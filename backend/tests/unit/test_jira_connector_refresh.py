"""Test to verify that <PERSON>ra connector refresh frequency issue is fixed."""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, MagicMock

from onyx.background.celery.tasks.indexing.utils import _should_index
from onyx.configs.app_configs import DEFAULT_CONNECTOR_REFRESH_FREQ
from onyx.configs.constants import DocumentSource
from onyx.db.connector_credential_pair import get_last_successful_attempt_time
from onyx.db.enums import ConnectorCredentialPairStatus, IndexingStatus, IndexModelStatus
from onyx.db.models import Connector, ConnectorCredentialPair, IndexAttempt, SearchSettings


def test_should_index_with_null_refresh_freq():
    """Test that connectors with null refresh_freq still get scheduled for indexing."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # This is the key issue - null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # Create a mock last index attempt that happened more than the default refresh frequency ago
    last_index = Mock(spec=IndexAttempt)
    last_index.status = IndexingStatus.SUCCESS
    last_index.time_updated = datetime.now(timezone.utc) - timedelta(seconds=DEFAULT_CONNECTOR_REFRESH_FREQ + 60)
    
    # Mock the database session
    db_session = Mock()
    
    # Mock get_db_current_time to return current time
    from onyx.background.celery.tasks.indexing import utils
    original_get_db_current_time = utils.get_db_current_time
    utils.get_db_current_time = Mock(return_value=datetime.now(timezone.utc))
    
    try:
        # Test that indexing should be triggered despite null refresh_freq
        result = _should_index(
            cc_pair=cc_pair,
            last_index=last_index,
            search_settings_instance=search_settings,
            search_settings_primary=True,
            secondary_index_building=False,
            db_session=db_session
        )
        
        assert result is True, "Connector with null refresh_freq should still be scheduled for indexing"
        
    finally:
        # Restore original function
        utils.get_db_current_time = original_get_db_current_time


def test_should_index_with_null_refresh_freq_too_recent():
    """Test that connectors with null refresh_freq don't get scheduled if last index was too recent."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # This is the key issue - null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # Create a mock last index attempt that happened recently (within default refresh frequency)
    last_index = Mock(spec=IndexAttempt)
    last_index.status = IndexingStatus.SUCCESS
    last_index.time_updated = datetime.now(timezone.utc) - timedelta(seconds=DEFAULT_CONNECTOR_REFRESH_FREQ - 60)
    
    # Mock the database session
    db_session = Mock()
    
    # Mock get_db_current_time to return current time
    from onyx.background.celery.tasks.indexing import utils
    original_get_db_current_time = utils.get_db_current_time
    utils.get_db_current_time = Mock(return_value=datetime.now(timezone.utc))
    
    try:
        # Test that indexing should NOT be triggered if last index was too recent
        result = _should_index(
            cc_pair=cc_pair,
            last_index=last_index,
            search_settings_instance=search_settings,
            search_settings_primary=True,
            secondary_index_building=False,
            db_session=db_session
        )
        
        assert result is False, "Connector should not be scheduled if last index was too recent"
        
    finally:
        # Restore original function
        utils.get_db_current_time = original_get_db_current_time


def test_should_index_no_previous_attempt():
    """Test that connectors with no previous attempts always get scheduled."""
    
    # Create mock objects
    connector = Mock(spec=Connector)
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # null refresh frequency
    
    cc_pair = Mock(spec=ConnectorCredentialPair)
    cc_pair.connector = connector
    cc_pair.status = ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.PRESENT
    
    # No previous index attempt
    last_index = None
    
    # Mock the database session
    db_session = Mock()
    
    # Test that indexing should be triggered for first-time indexing
    result = _should_index(
        cc_pair=cc_pair,
        last_index=last_index,
        search_settings_instance=search_settings,
        search_settings_primary=True,
        secondary_index_building=False,
        db_session=db_session
    )
    
    assert result is True, "Connector with no previous attempts should always be scheduled"


def test_get_last_successful_attempt_time_uses_poll_range_end():
    """Test that get_last_successful_attempt_time uses poll_range_end when available."""

    # Create mock objects
    connector_id = 1
    credential_id = 1
    earliest_index = 1000.0

    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.FUTURE  # This forces secondary index logic
    search_settings.id = 1

    # Create a mock index attempt with poll_range_end
    poll_end_time = datetime.now(timezone.utc) - timedelta(hours=1)
    start_time = datetime.now(timezone.utc) - timedelta(hours=2)

    mock_attempt = Mock(spec=IndexAttempt)
    mock_attempt.poll_range_end = poll_end_time
    mock_attempt.time_started = start_time

    # Mock the database session and query
    db_session = Mock()
    mock_query = Mock()
    mock_query.join.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query
    mock_query.first.return_value = mock_attempt

    db_session.query.return_value = mock_query

    # Test that poll_range_end is used instead of time_started
    result = get_last_successful_attempt_time(
        connector_id=connector_id,
        credential_id=credential_id,
        earliest_index=earliest_index,
        search_settings=search_settings,
        db_session=db_session
    )

    expected_timestamp = poll_end_time.timestamp()
    assert result == expected_timestamp, f"Expected {expected_timestamp}, got {result}"
    assert result != start_time.timestamp(), "Should not use time_started when poll_range_end is available"


def test_get_last_successful_attempt_time_fallback_to_time_started():
    """Test that get_last_successful_attempt_time falls back to time_started when poll_range_end is None."""

    # Create mock objects
    connector_id = 1
    credential_id = 1
    earliest_index = 1000.0

    search_settings = Mock(spec=SearchSettings)
    search_settings.status = IndexModelStatus.FUTURE  # This forces secondary index logic
    search_settings.id = 1

    # Create a mock index attempt without poll_range_end
    start_time = datetime.now(timezone.utc) - timedelta(hours=2)

    mock_attempt = Mock(spec=IndexAttempt)
    mock_attempt.poll_range_end = None  # No poll range end
    mock_attempt.time_started = start_time

    # Mock the database session and query
    db_session = Mock()
    mock_query = Mock()
    mock_query.join.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query
    mock_query.first.return_value = mock_attempt

    db_session.query.return_value = mock_query

    # Test that time_started is used when poll_range_end is None
    result = get_last_successful_attempt_time(
        connector_id=connector_id,
        credential_id=credential_id,
        earliest_index=earliest_index,
        search_settings=search_settings,
        db_session=db_session
    )

    expected_timestamp = start_time.timestamp()
    assert result == expected_timestamp, f"Expected {expected_timestamp}, got {result}"
