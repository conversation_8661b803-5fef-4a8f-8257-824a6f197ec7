#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test the Jira connector fix for missing new documents.
This script simulates the issue and verifies that the fix works.
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from onyx.configs.app_configs import DEFAULT_CONNECTOR_REFRESH_FREQ
from onyx.configs.constants import DocumentSource


def test_refresh_frequency_fix():
    """Test that the refresh frequency fix works correctly."""
    print("Testing refresh frequency fix...")
    
    # Test 1: Verify DEFAULT_CONNECTOR_REFRESH_FREQ is defined
    print(f"DEFAULT_CONNECTOR_REFRESH_FREQ = {DEFAULT_CONNECTOR_REFRESH_FREQ} seconds")
    assert DEFAULT_CONNECTOR_REFRESH_FREQ == 30 * 60, "Default refresh frequency should be 30 minutes"
    
    # Test 2: Simulate the _should_index logic
    from onyx.background.celery.tasks.indexing.utils import _should_index
    from unittest.mock import Mock
    
    # Create mock objects
    connector = Mock()
    connector.id = 1
    connector.source = DocumentSource.JIRA
    connector.refresh_freq = None  # This was the problematic case
    
    cc_pair = Mock()
    cc_pair.connector = connector
    cc_pair.status = "ACTIVE"  # ConnectorCredentialPairStatus.ACTIVE
    cc_pair.indexing_trigger = None
    
    search_settings = Mock()
    search_settings.status = "PRESENT"  # IndexModelStatus.PRESENT
    
    # Create a mock last index attempt that happened more than default refresh frequency ago
    last_index = Mock()
    last_index.status = "SUCCESS"  # IndexingStatus.SUCCESS
    last_index.time_updated = datetime.now(timezone.utc) - timedelta(seconds=DEFAULT_CONNECTOR_REFRESH_FREQ + 60)
    
    db_session = Mock()
    
    # Mock get_db_current_time
    from onyx.background.celery.tasks.indexing import utils
    original_get_db_current_time = utils.get_db_current_time
    utils.get_db_current_time = Mock(return_value=datetime.now(timezone.utc))
    
    try:
        # Test that indexing should be triggered despite null refresh_freq
        result = _should_index(
            cc_pair=cc_pair,
            last_index=last_index,
            search_settings_instance=search_settings,
            search_settings_primary=True,
            secondary_index_building=False,
            db_session=db_session
        )
        
        print(f"_should_index result with null refresh_freq: {result}")
        assert result is True, "Connector with null refresh_freq should still be scheduled for indexing"
        print("✓ Refresh frequency fix working correctly!")
        
    finally:
        # Restore original function
        utils.get_db_current_time = original_get_db_current_time


def test_polling_window_fix():
    """Test that the polling window fix works correctly."""
    print("\nTesting polling window fix...")
    
    from onyx.db.connector_credential_pair import get_last_successful_attempt_time
    from unittest.mock import Mock
    
    # Test case: Secondary index with poll_range_end available
    connector_id = 1
    credential_id = 1
    earliest_index = 1000.0
    
    search_settings = Mock()
    search_settings.status = "FUTURE"  # IndexModelStatus.FUTURE (secondary index)
    search_settings.id = 1
    
    # Create a mock index attempt with poll_range_end
    poll_end_time = datetime.now(timezone.utc) - timedelta(hours=1)
    start_time = datetime.now(timezone.utc) - timedelta(hours=2)
    
    mock_attempt = Mock()
    mock_attempt.poll_range_end = poll_end_time
    mock_attempt.time_started = start_time
    
    # Mock the database session and query
    db_session = Mock()
    mock_query = Mock()
    mock_query.join.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query
    mock_query.first.return_value = mock_attempt
    
    db_session.query.return_value = mock_query
    
    # Test that poll_range_end is used instead of time_started
    result = get_last_successful_attempt_time(
        connector_id=connector_id,
        credential_id=credential_id,
        earliest_index=earliest_index,
        search_settings=search_settings,
        db_session=db_session
    )
    
    expected_timestamp = poll_end_time.timestamp()
    print(f"Expected timestamp: {expected_timestamp}")
    print(f"Actual timestamp: {result}")
    print(f"Start time timestamp: {start_time.timestamp()}")
    
    assert result == expected_timestamp, f"Expected {expected_timestamp}, got {result}"
    assert result != start_time.timestamp(), "Should not use time_started when poll_range_end is available"
    print("✓ Polling window fix working correctly!")


def main():
    """Run all tests."""
    print("Running Jira connector fix tests...\n")
    
    try:
        test_refresh_frequency_fix()
        test_polling_window_fix()
        print("\n🎉 All tests passed! The Jira connector fix should work correctly.")
        print("\nSummary of fixes:")
        print("1. ✓ Null refresh_freq now defaults to 30 minutes instead of blocking indexing")
        print("2. ✓ Polling window now uses poll_range_end instead of time_started to avoid gaps")
        print("3. ✓ Frontend no longer sets refresh_freq to null unnecessarily")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
